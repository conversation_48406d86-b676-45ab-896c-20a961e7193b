import React, { useEffect } from "react";
import AnnouncementCard from "./announcementCard";
import { useDispatch } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  fetchAllAnnouncements,
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
} from "@/store/slices/org-announcement/announcement";
import { useSelector } from "react-redux";
import CustomDropdown from "../common/DropDown";
import DateRangePicker from "../common/DatePicker";
import { Button } from "../ui/button";
import FloatingMessages from "../common/FloatingMessages";
import { useMessages } from "@/hooks/useMessages";
// Import the new messages Redux functionality
import { useMessagesRedux } from "@/hooks/useMessagesRedux";
import { fetchConnectedUsersFromStorage } from "@/store/slices/messagesSlice";

const Index: React.FC = () => {
  // Existing state
  const [selectedType, setSelectedType] = React.useState<string>("");
  const [selectedCategory, setSelectedCategory] = React.useState<string>("");
  const [selectedStatus, setSelectedStatus] = React.useState<string>("");
  const [startDate, setStartDate] = React.useState<Date | null>(null);
  const [endDate, setEndDate] = React.useState<Date | null>(null);

  // Messages hook
  const { messages, addMessage, removeMessage } = useMessages();

  // Connected Users Redux hook
  const {
    loading: usersLoading,
    error: usersError,
    connectedUsers,
    fetchConnectedUsersFromLocalStorage,
  } = useMessagesRedux();

  const {
    orgAnnouncement,
    annoucementType,
    allAnnouncements,
    deleteAnnouncement,
  } = useSelector((state: RootState) => state.announcementDashboard);
  const dispatch = useDispatch<AppDispatch>();

  const handleAnnouncementTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedType(e.target.value);
  };

  const handleCategoryTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedCategory(e.target.value);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStatus(e.target.value);
  };

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setStartDate(dates[0]);
    setEndDate(dates[1]);
  };

  const statusOptions = [
    {
      label: "Publish",
      value: "Publish",
    },
    {
      label: "Unpublish",
      value: "Unpublish",
    },
  ];

  useEffect(() => {
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
    dispatch(fetchAllAnnouncements());

    // Fetch connected users using Redux
    fetchConnectedUsersFromLocalStorage();
  }, [fetchConnectedUsersFromLocalStorage]);

  const handleDelete = async (id: number) => {
    try {
      await dispatch(deleteAnnouncement({ id }));
      dispatch(fetchAllAnnouncements());
      addMessage("Announcement deleted successfully", "success");
    } catch (error) {
      addMessage("Failed to delete announcement", "error");
    }
  };

  const handleSubmit = () => {
    // Example of showing a message
    addMessage("Filters applied successfully", "info");
  };

  return (
    <div className="min-h-screen bg-white py-14 px-4 md:px-24">
      <div className="w-full max-w-6xl mx-auto p-0 bg-white rounded-md">
        <form className="w-full grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-12 items-end bg-gradient-to-br from-gray-200 via-gray-300 to-gray-400 rounded-2xl p-10 shadow-xl mx-auto mt-4 transition-all duration-300 hover:shadow-2xl transform hover:scale-[1.01]">
          {/* Announcement Type */}
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-800 mb-1">
              Announcement Type
            </label>
            <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
              <CustomDropdown
                options={annoucementType?.data?.map((item) => item?.typeName)}
                selectedValue={selectedType}
                handleChange={handleAnnouncementTypeChange}
              />
            </div>
          </div>

          {/* Date Range */}
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-800 mb-1">
              Date Range
            </label>
            <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onDateChange={handleDateChange}
              />
            </div>
          </div>

          {/* Announcement Category */}
          <div className="flex flex-col">
            <label className="text-sm font-semibold text-gray-800 mb-1">
              Announcement Category
            </label>
            <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
              <CustomDropdown
                options={orgAnnouncement?.data?.map(
                  (item) => item?.categoryName
                )}
                selectedValue={selectedCategory}
                handleChange={handleCategoryTypeChange}
              />
            </div>
          </div>

          {/* Status + Submit Button */}
          <div className="flex items-end gap-4">
            <div className="flex-1">
              <label className="text-sm font-semibold text-gray-800 mb-1">
                Status
              </label>
              <div className="transition-all duration-200 hover:shadow-md hover:scale-[1.01] rounded-md">
                <CustomDropdown
                  options={statusOptions?.map((item) => item?.value)}
                  selectedValue={selectedStatus}
                  handleChange={handleStatusChange}
                />
              </div>
            </div>
            <Button 
              className="bg-indigo-600 hover:bg-indigo-700 text-white transition duration-300 shadow-md hover:shadow-lg transform hover:scale-105 px-5 py-2.5 rounded-md"
              onClick={handleSubmit}
              type="button"
            >
              <span className="text-sm font-semibold">Submit</span>
            </Button>
          </div>
        </form>
      </div>

      {allAnnouncements?.data?.map((item) => {
        return (
          <AnnouncementCard key={item.id} data={item} onDelete={handleDelete} />
        );
      })}

      {/* Connected Users Demo Section */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          Connected Users (Redux Demo)
        </h3>
        <div className="space-y-2">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">Status:</span>
            <span className={`text-sm ${usersLoading ? 'text-yellow-600' : 'text-green-600'}`}>
              {usersLoading ? 'Loading...' : 'Loaded'}
            </span>
            <span className="text-sm font-medium">Count:</span>
            <span className="text-sm text-blue-700">{connectedUsers.length}</span>
          </div>

          {usersError && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              Error: {usersError}
            </div>
          )}

          {connectedUsers.length > 0 && (
            <div className="text-sm">
              <strong>Sample Users:</strong>
              <ul className="mt-1 space-y-1">
                {connectedUsers.slice(0, 3).map((user) => (
                  <li key={user.id} className="text-gray-700">
                    • {user.firstName} {user.lastName} ({user.email})
                  </li>
                ))}
                {connectedUsers.length > 3 && (
                  <li className="text-gray-500">
                    ... and {connectedUsers.length - 3} more users
                  </li>
                )}
              </ul>
            </div>
          )}

          <Button
            onClick={fetchConnectedUsersFromLocalStorage}
            disabled={usersLoading}
            size="sm"
            variant="outline"
          >
            Refresh Connected Users
          </Button>
        </div>
      </div>

      {/* Floating Messages Component */}
      <FloatingMessages messages={messages} onDismiss={removeMessage} />
    </div>
  );
};

export default Index;
