"use client";

import { useEffect, useState } from 'react';
import { useMessagesRedux } from '@/hooks/useMessagesRedux';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { RefreshCw, User, Mail, AlertCircle, CheckCircle } from 'lucide-react';

const ConnectedUsersRedux = () => {
  const [userIdInput, setUserIdInput] = useState('170');
  
  const {
    loading,
    error,
    connectedUsers,
    selectedUserId,
    selectedUser,
    fetchConnectedUsersByUserId,
    fetchConnectedUsersFromLocalStorage,
    selectUser,
    clearUsers,
    clearErrorMessage,
    getUserById,
  } = useMessagesRedux();

  // Auto-fetch from localStorage on component mount
  useEffect(() => {
    fetchConnectedUsersFromLocalStorage();
  }, [fetchConnectedUsersFromLocalStorage]);

  const handleFetchByUserId = () => {
    if (userIdInput.trim()) {
      fetchConnectedUsersByUserId(userIdInput.trim());
    }
  };

  const handleUserSelect = (userId: number) => {
    selectUser(userId);
  };

  const handleClearError = () => {
    clearErrorMessage();
  };

  return (
    <div className="space-y-6 p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Connected Users - Redux Implementation</span>
            {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
          </CardTitle>
          <CardDescription>
            Fetch connected users using Redux async thunk from API endpoint
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Manual User ID Input */}
          <div className="flex space-x-2">
            <Input
              type="text"
              placeholder="Enter User ID (e.g., 170)"
              value={userIdInput}
              onChange={(e) => setUserIdInput(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleFetchByUserId}
              disabled={loading || !userIdInput.trim()}
            >
              Fetch by ID
            </Button>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button 
              onClick={fetchConnectedUsersFromLocalStorage}
              disabled={loading}
              variant="outline"
            >
              Fetch from localStorage
            </Button>
            <Button 
              onClick={clearUsers}
              disabled={loading}
              variant="outline"
            >
              Clear Users
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-red-600" />
                <span className="text-red-700 text-sm">{error}</span>
              </div>
              <Button size="sm" variant="ghost" onClick={handleClearError}>
                ✕
              </Button>
            </div>
          )}

          {/* Success Message */}
          {!loading && !error && connectedUsers.length > 0 && (
            <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-green-700 text-sm">
                Successfully loaded {connectedUsers.length} connected users
              </span>
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="text-center py-4">
              <RefreshCw className="w-6 h-6 mx-auto mb-2 animate-spin text-blue-600" />
              <p className="text-sm text-gray-600">Loading connected users...</p>
            </div>
          )}

          {/* Connected Users List */}
          {!loading && connectedUsers.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium text-gray-900">
                Connected Users ({connectedUsers.length})
              </h3>
              <div className="max-h-60 overflow-y-auto space-y-2">
                {connectedUsers.map((user) => (
                  <div
                    key={user.id}
                    onClick={() => handleUserSelect(user.id)}
                    className={`p-3 border rounded-md cursor-pointer transition-colors ${
                      selectedUserId === user.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-slate-700 text-white rounded-full flex items-center justify-center">
                        {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </h4>
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                            ID: {user.id}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Mail className="w-3 h-3" />
                          <span>{user.email}</span>
                        </div>
                        {user.galleries && user.galleries.length > 0 && (
                          <p className="text-xs text-blue-600">
                            {user.galleries.length} galleries
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Selected User Details */}
          {selectedUser && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="font-medium text-blue-900 mb-2">Selected User</h3>
              <div className="space-y-1 text-sm">
                <p><strong>Name:</strong> {selectedUser.firstName} {selectedUser.lastName}</p>
                <p><strong>Email:</strong> {selectedUser.email}</p>
                <p><strong>ID:</strong> {selectedUser.id}</p>
                <p><strong>Galleries:</strong> {selectedUser.galleries?.length || 0}</p>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && connectedUsers.length === 0 && (
            <div className="text-center py-8">
              <User className="w-12 h-12 mx-auto mb-3 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-1">No Connected Users</h3>
              <p className="text-gray-500">
                Click "Fetch from localStorage" or enter a User ID to load connected users
              </p>
            </div>
          )}

          {/* API Information */}
          <div className="text-xs text-gray-500 space-y-1 pt-4 border-t">
            <p><strong>API Endpoint:</strong></p>
            <p className="font-mono bg-gray-100 p-2 rounded break-all">
              https://api.engageathlete.com/api/notification/v1/messages/connecteduser/{userIdInput || '{userId}'}
            </p>
            <p><strong>Redux State:</strong> messages.connectedUsers</p>
            <p><strong>Loading:</strong> {loading ? 'true' : 'false'}</p>
            <p><strong>Error:</strong> {error || 'none'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConnectedUsersRedux;
