import { useDispatch, useSelector } from 'react-redux';
import { useCallback } from 'react';
import { RootState, AppDispatch } from '@/store';
import { 
  fetchConnectedUsers, 
  fetchConnectedUsersFromStorage,
  setSelectedUser,
  clearConnectedUsers,
  clearError,
  ConnectedUser
} from '@/store/slices/messagesSlice';

// Custom hook to use messages Redux slice
export const useMessagesRedux = () => {
  const dispatch = useDispatch<AppDispatch>();
  
  // Select state from Redux store
  const {
    loading,
    error,
    connectedUsers,
    selectedUserId
  } = useSelector((state: RootState) => state.messages);

  // Fetch connected users by userId
  const fetchConnectedUsersByUserId = useCallback((userId: string | number) => {
    dispatch(fetchConnectedUsers(userId));
  }, [dispatch]);

  // Fetch connected users from localStorage
  const fetchConnectedUsersFromLocalStorage = useCallback(() => {
    dispatch(fetchConnectedUsersFromStorage());
  }, [dispatch]);

  // Set selected user
  const selectUser = useCallback((userId: number) => {
    dispatch(setSelectedUser(userId));
  }, [dispatch]);

  // Clear connected users
  const clearUsers = useCallback(() => {
    dispatch(clearConnectedUsers());
  }, [dispatch]);

  // Clear error
  const clearErrorMessage = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Get selected user object
  const selectedUser = connectedUsers.find(user => user.id === selectedUserId) || null;

  // Get user by ID
  const getUserById = useCallback((userId: number): ConnectedUser | null => {
    return connectedUsers.find(user => user.id === userId) || null;
  }, [connectedUsers]);

  return {
    // State
    loading,
    error,
    connectedUsers,
    selectedUserId,
    selectedUser,
    
    // Actions
    fetchConnectedUsersByUserId,
    fetchConnectedUsersFromLocalStorage,
    selectUser,
    clearUsers,
    clearErrorMessage,
    getUserById,
  };
};
