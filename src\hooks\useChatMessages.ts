import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { ChatMessage, Conversation } from '@/components/messages/MessagesScreen';

// Mock data for demonstration
const mockConversations: Conversation[] = [
  {
    id: '1',
    participantId: 'user1',
    participantName: '<PERSON>',
    participantAvatar: '',
    participantRole: 'athlete',
    unreadCount: 2,
    isOnline: true,
    lastMessage: {
      id: 'msg1',
      senderId: 'user1',
      senderName: '<PERSON>',
      content: 'Hey! Are you available for training this weekend?',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      isRead: false,
    },
  },
  {
    id: '2',
    participantId: 'user2',
    participantName: '<PERSON>',
    participantAvatar: '',
    participantRole: 'coach',
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    lastMessage: {
      id: 'msg2',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Thanks for the feedback on my performance!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      isRead: true,
    },
  },
  {
    id: '3',
    participantId: 'user3',
    participantName: 'Elite Sports Academy',
    participantAvatar: '',
    participantRole: 'sports organization',
    unreadCount: 1,
    isOnline: true,
    lastMessage: {
      id: 'msg3',
      senderId: 'user3',
      senderName: 'Elite Sports Academy',
      content: 'We have an exciting opportunity for you. Would you like to discuss?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      isRead: false,
    },
  },
  {
    id: '4',
    participantId: 'user4',
    participantName: 'Alex Rodriguez',
    participantAvatar: '',
    participantRole: 'athlete',
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    lastMessage: {
      id: 'msg4',
      senderId: 'user4',
      senderName: 'Alex Rodriguez',
      content: 'Great game yesterday! Looking forward to the next match.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      isRead: true,
    },
  },
  {
    id: '5',
    participantId: 'user5',
    participantName: 'Dr. Emma Wilson',
    participantAvatar: '',
    participantRole: 'sports medicine',
    unreadCount: 3,
    isOnline: true,
    lastMessage: {
      id: 'msg5',
      senderId: 'user5',
      senderName: 'Dr. Emma Wilson',
      content: 'Your recovery plan is ready. Please review the attached document.',
      timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
      isRead: false,
    },
  },
  {
    id: '6',
    participantId: 'user6',
    participantName: 'Marcus Thompson',
    participantAvatar: '',
    participantRole: 'coach',
    unreadCount: 1,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
    lastMessage: {
      id: 'msg6',
      senderId: 'user6',
      senderName: 'Marcus Thompson',
      content: 'Team meeting tomorrow at 9 AM. Don\'t forget!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: false,
    },
  },
  {
    id: '7',
    participantId: 'user7',
    participantName: 'Olympic Training Center',
    participantAvatar: '',
    participantRole: 'sports organization',
    unreadCount: 0,
    isOnline: true,
    lastMessage: {
      id: 'msg7',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Thank you for the invitation. I\'m very interested!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      isRead: true,
    },
  },
  {
    id: '8',
    participantId: 'user8',
    participantName: 'Jessica Chen',
    participantAvatar: '',
    participantRole: 'nutritionist',
    unreadCount: 2,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 1), // 1 hour ago
    lastMessage: {
      id: 'msg8',
      senderId: 'user8',
      senderName: 'Jessica Chen',
      content: 'Your meal plan is updated. Check out these new recipes: www.healthyeats.com/athletes',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1), // 1 hour ago
      isRead: false,
    },
  },
  {
    id: '9',
    participantId: 'user9',
    participantName: 'David Park',
    participantAvatar: '',
    participantRole: 'athlete',
    unreadCount: 0,
    isOnline: true,
    lastMessage: {
      id: 'msg9',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Good luck with your competition this weekend!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      isRead: true,
    },
  },
  {
    id: '10',
    participantId: 'user10',
    participantName: 'Sports Analytics Pro',
    participantAvatar: '',
    participantRole: 'sports organization',
    unreadCount: 1,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
    lastMessage: {
      id: 'msg10',
      senderId: 'user10',
      senderName: 'Sports Analytics Pro',
      content: 'Your performance analytics report is ready for download.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
      isRead: false,
    },
  },
  {
    id: '11',
    participantId: 'user11',
    participantName: 'Coach Maria Santos',
    participantAvatar: '',
    participantRole: 'coach',
    unreadCount: 0,
    isOnline: true,
    lastMessage: {
      id: 'msg11',
      senderId: 'user11',
      senderName: 'Coach Maria Santos',
      content: 'Excellent progress this week! Keep it up.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
      isRead: true,
    },
  },
  {
    id: '12',
    participantId: 'user12',
    participantName: 'Tom Wilson',
    participantAvatar: '',
    participantRole: 'athlete',
    unreadCount: 4,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    lastMessage: {
      id: 'msg12',
      senderId: 'user12',
      senderName: 'Tom Wilson',
      content: 'Can we schedule a training session for next week?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: false,
    },
  },
  {
    id: '13',
    participantId: 'user13',
    participantName: 'Lisa Chang',
    participantAvatar: '',
    participantRole: 'physiotherapist',
    unreadCount: 1,
    isOnline: true,
    lastMessage: {
      id: 'msg13',
      senderId: 'user13',
      senderName: 'Lisa Chang',
      content: 'Your mobility exercises are showing great results!',
      timestamp: new Date(Date.now() - 1000 * 60 * 20), // 20 minutes ago
      isRead: false,
    },
  },
  {
    id: '14',
    participantId: 'user14',
    participantName: 'Global Sports Network',
    participantAvatar: '',
    participantRole: 'sports organization',
    unreadCount: 0,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
    lastMessage: {
      id: 'msg14',
      senderId: 'current-user',
      senderName: 'You',
      content: 'I\'ll review the sponsorship proposal and get back to you.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
      isRead: true,
    },
  },
  {
    id: '15',
    participantId: 'user15',
    participantName: 'Ryan Mitchell',
    participantAvatar: '',
    participantRole: 'athlete',
    unreadCount: 2,
    isOnline: true,
    lastMessage: {
      id: 'msg15',
      senderId: 'user15',
      senderName: 'Ryan Mitchell',
      content: 'Want to train together tomorrow morning?',
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      isRead: false,
    },
  },
];

const mockMessages: Record<string, ChatMessage[]> = {
  '1': [
    {
      id: 'msg1-1',
      senderId: 'user1',
      senderName: 'Sarah Johnson',
      content: 'Hi! How are you doing?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: true,
    },
    {
      id: 'msg1-2',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Hey Sarah! I\'m doing great, thanks for asking. How about you?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1.5), // 1.5 hours ago
      isRead: true,
    },
    {
      id: 'msg1-3',
      senderId: 'user1',
      senderName: 'Sarah Johnson',
      content: 'I\'m good too! Hey, are you available for training this weekend?',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      isRead: false,
    },
    {
      id: 'msg1-4',
      senderId: 'user1',
      senderName: 'Sarah Johnson',
      content: 'I was thinking we could work on some new techniques.',
      timestamp: new Date(Date.now() - 1000 * 60 * 25), // 25 minutes ago
      isRead: false,
    },
    {
      id: 'msg1-5',
      senderId: 'user1',
      senderName: 'Sarah Johnson',
      content: 'Here\'s my training schedule for reference',
      timestamp: new Date(Date.now() - 1000 * 60 * 20), // 20 minutes ago
      isRead: false,
      attachment: {
        file: new File([''], 'training-schedule.pdf', { type: 'application/pdf' }),
        type: 'pdf' as const,
        url: 'data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVGl0bGUgKFRyYWluaW5nIFNjaGVkdWxlKQo+PgplbmRvYmoKJSVFT0Y=',
        name: 'training-schedule.pdf',
        size: 245760, // 240KB
      },
    },
  ],
  '2': [
    {
      id: 'msg2-1',
      senderId: 'user2',
      senderName: 'Mike Chen',
      content: 'Great job in today\'s training session!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
      isRead: true,
    },
    {
      id: 'msg2-2',
      senderId: 'user2',
      senderName: 'Mike Chen',
      content: 'Your form has improved significantly. Keep up the good work!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4.5), // 4.5 hours ago
      isRead: true,
    },
    {
      id: 'msg2-3',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Thanks for the feedback on my performance!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      isRead: true,
    },
    {
      id: 'msg2-4',
      senderId: 'current-user',
      senderName: 'You',
      content: 'I found this great training video: https://www.youtube.com/watch?v=example and also check out this article: www.sportsscience.com/training-tips',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3.5), // 3.5 hours ago
      isRead: true,
    },
    {
      id: 'msg2-5',
      senderId: 'user2',
      senderName: 'Mike Chen',
      content: 'Great resources! Also take a look at this nutrition guide: https://nutrition.example.com/athletes',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
      isRead: true,
    },
  ],
  '3': [
    {
      id: 'msg3-1',
      senderId: 'user3',
      senderName: 'Elite Sports Academy',
      content: 'Hello! We\'ve been following your athletic progress.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
      isRead: true,
    },
    {
      id: 'msg3-2',
      senderId: 'user3',
      senderName: 'Elite Sports Academy',
      content: 'We have an exciting opportunity for you. Would you like to discuss?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      isRead: false,
    },
    {
      id: 'msg3-3',
      senderId: 'user3',
      senderName: 'Elite Sports Academy',
      content: 'Here\'s our facility overview',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
      isRead: false,
      attachment: {
        file: new File([''], 'facility-overview.jpg', { type: 'image/jpeg' }),
        type: 'image' as const,
        url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
        name: 'facility-overview.jpg',
        size: 156800, // 153KB
      },
    },
  ],
  '4': [
    {
      id: 'msg4-1',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Hey Alex! How did your match go yesterday?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 25), // 25 hours ago
      isRead: true,
    },
    {
      id: 'msg4-2',
      senderId: 'user4',
      senderName: 'Alex Rodriguez',
      content: 'Great game yesterday! Looking forward to the next match.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      isRead: true,
    },
    {
      id: 'msg4-3',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Check out these resources: https://www.fifa.com/training, www.soccerdrills.com and also espn.com/soccer for latest news!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 23), // 23 hours ago
      isRead: true,
    },
  ],
  '5': [
    {
      id: 'msg5-1',
      senderId: 'user5',
      senderName: 'Dr. Emma Wilson',
      content: 'Hello! I\'ve reviewed your recent injury report.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: true,
    },
    {
      id: 'msg5-2',
      senderId: 'user5',
      senderName: 'Dr. Emma Wilson',
      content: 'Your recovery plan is ready. Please review the attached document.',
      timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
      isRead: false,
      attachment: {
        file: new File([''], 'recovery-plan.pdf', { type: 'application/pdf' }),
        type: 'pdf' as const,
        url: 'data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVGl0bGUgKFJlY292ZXJ5IFBsYW4pCj4+CmVuZG9iagolJUVPRg==',
        name: 'recovery-plan.pdf',
        size: 512000, // 500KB
      },
    },
    {
      id: 'msg5-3',
      senderId: 'user5',
      senderName: 'Dr. Emma Wilson',
      content: 'Also, here\'s a helpful resource: https://www.sportsmed.org/rehabilitation',
      timestamp: new Date(Date.now() - 1000 * 60 * 40), // 40 minutes ago
      isRead: false,
    },
  ],
  '6': [
    {
      id: 'msg6-1',
      senderId: 'user6',
      senderName: 'Marcus Thompson',
      content: 'Great practice session today! Your technique is improving.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
      isRead: true,
    },
    {
      id: 'msg6-2',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Thank you! I\'ve been working on those drills you suggested.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      isRead: true,
    },
    {
      id: 'msg6-3',
      senderId: 'user6',
      senderName: 'Marcus Thompson',
      content: 'Team meeting tomorrow at 9 AM. Don\'t forget!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: false,
    },
  ],
  '7': [
    {
      id: 'msg7-1',
      senderId: 'user7',
      senderName: 'Olympic Training Center',
      content: 'We\'re impressed with your recent performance and would like to invite you to our elite training program.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      isRead: true,
    },
    {
      id: 'msg7-2',
      senderId: 'user7',
      senderName: 'Olympic Training Center',
      content: 'Here\'s more information about our facilities: https://www.olympictraining.org/programs',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
      isRead: true,
    },
    {
      id: 'msg7-3',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Thank you for the invitation. I\'m very interested!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      isRead: true,
    },
  ],
  '8': [
    {
      id: 'msg8-1',
      senderId: 'user8',
      senderName: 'Jessica Chen',
      content: 'Hi! I\'ve prepared your personalized nutrition plan.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
      isRead: true,
    },
    {
      id: 'msg8-2',
      senderId: 'user8',
      senderName: 'Jessica Chen',
      content: 'Your meal plan is updated. Check out these new recipes: www.healthyeats.com/athletes',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1), // 1 hour ago
      isRead: false,
    },
    {
      id: 'msg8-3',
      senderId: 'user8',
      senderName: 'Jessica Chen',
      content: 'Let me know if you have any questions about the supplements.',
      timestamp: new Date(Date.now() - 1000 * 60 * 55), // 55 minutes ago
      isRead: false,
    },
  ],
  '9': [
    {
      id: 'msg9-1',
      senderId: 'user9',
      senderName: 'David Park',
      content: 'Hey! How\'s your training going?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
      isRead: true,
    },
    {
      id: 'msg9-2',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Going well! I have a big competition coming up this weekend.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 7), // 7 hours ago
      isRead: true,
    },
    {
      id: 'msg9-3',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Good luck with your competition this weekend!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      isRead: true,
    },
  ],
  '10': [
    {
      id: 'msg10-1',
      senderId: 'user10',
      senderName: 'Sports Analytics Pro',
      content: 'Your weekly performance data has been analyzed.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 10), // 10 hours ago
      isRead: true,
    },
    {
      id: 'msg10-2',
      senderId: 'user10',
      senderName: 'Sports Analytics Pro',
      content: 'Your performance analytics report is ready for download.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
      isRead: false,
      attachment: {
        file: new File([''], 'performance-report.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }),
        type: 'document' as const,
        url: 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,UEsDBBQAAAAIAA==',
        name: 'performance-report.xlsx',
        size: 1024000, // 1MB
      },
    },
  ],
  '11': [
    {
      id: 'msg11-1',
      senderId: 'user11',
      senderName: 'Coach Maria Santos',
      content: 'I\'ve been reviewing your progress from this week.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 14), // 14 hours ago
      isRead: true,
    },
    {
      id: 'msg11-2',
      senderId: 'user11',
      senderName: 'Coach Maria Santos',
      content: 'Excellent progress this week! Keep it up.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
      isRead: true,
    },
    {
      id: 'msg11-3',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Thank you! Your guidance has been incredibly helpful.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 11), // 11 hours ago
      isRead: true,
    },
  ],
  '12': [
    {
      id: 'msg12-1',
      senderId: 'user12',
      senderName: 'Tom Wilson',
      content: 'Hey! I saw your recent competition results. Congratulations!',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      isRead: true,
    },
    {
      id: 'msg12-2',
      senderId: 'user12',
      senderName: 'Tom Wilson',
      content: 'Can we schedule a training session for next week?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: false,
    },
    {
      id: 'msg12-3',
      senderId: 'user12',
      senderName: 'Tom Wilson',
      content: 'I\'d love to learn some of your techniques.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: false,
    },
    {
      id: 'msg12-4',
      senderId: 'user12',
      senderName: 'Tom Wilson',
      content: 'Also, check this training video I found: https://www.youtube.com/watch?v=training123',
      timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
      isRead: false,
    },
  ],
  '13': [
    {
      id: 'msg13-1',
      senderId: 'user13',
      senderName: 'Lisa Chang',
      content: 'How are you feeling after yesterday\'s session?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1), // 1 hour ago
      isRead: true,
    },
    {
      id: 'msg13-2',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Much better! The stretches you recommended really helped.',
      timestamp: new Date(Date.now() - 1000 * 60 * 40), // 40 minutes ago
      isRead: true,
    },
    {
      id: 'msg13-3',
      senderId: 'user13',
      senderName: 'Lisa Chang',
      content: 'Your mobility exercises are showing great results!',
      timestamp: new Date(Date.now() - 1000 * 60 * 20), // 20 minutes ago
      isRead: false,
    },
  ],
  '14': [
    {
      id: 'msg14-1',
      senderId: 'user14',
      senderName: 'Global Sports Network',
      content: 'We\'re excited to discuss a potential partnership with you.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
      isRead: true,
    },
    {
      id: 'msg14-2',
      senderId: 'user14',
      senderName: 'Global Sports Network',
      content: 'Here\'s our sponsorship proposal for your review.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 7), // 7 hours ago
      isRead: true,
      attachment: {
        file: new File([''], 'sponsorship-proposal.pdf', { type: 'application/pdf' }),
        type: 'pdf' as const,
        url: 'data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVGl0bGUgKFNwb25zb3JzaGlwIFByb3Bvc2FsKQo+PgplbmRvYmoKJSVFT0Y=',
        name: 'sponsorship-proposal.pdf',
        size: 2048000, // 2MB
      },
    },
    {
      id: 'msg14-3',
      senderId: 'current-user',
      senderName: 'You',
      content: 'I\'ll review the sponsorship proposal and get back to you.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
      isRead: true,
    },
  ],
  '15': [
    {
      id: 'msg15-1',
      senderId: 'user15',
      senderName: 'Ryan Mitchell',
      content: 'Hey! I heard you\'re preparing for the nationals too.',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRead: true,
    },
    {
      id: 'msg15-2',
      senderId: 'current-user',
      senderName: 'You',
      content: 'Yes! It\'s going to be intense. How\'s your preparation going?',
      timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
      isRead: true,
    },
    {
      id: 'msg15-3',
      senderId: 'user15',
      senderName: 'Ryan Mitchell',
      content: 'Pretty good! Want to train together tomorrow morning?',
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      isRead: false,
    },
    {
      id: 'msg15-4',
      senderId: 'user15',
      senderName: 'Ryan Mitchell',
      content: 'I found this great training spot: maps.google.com/training-facility',
      timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
      isRead: false,
    },
  ],
};

export const useChatMessages = () => {
  const [conversations, setConversations] = useState<Conversation[]>(mockConversations);
  const [messages, setMessages] = useState<Record<string, ChatMessage[]>>(mockMessages);

  const sendMessage = useCallback((conversationId: string, content: string, attachment?: { file: File; type: 'image' | 'document' | 'pdf'; url: string }) => {
    const newMessage: ChatMessage = {
      id: uuidv4(),
      senderId: 'current-user',
      senderName: 'You',
      content,
      timestamp: new Date(),
      isRead: true,
      attachment: attachment ? {
        file: attachment.file,
        type: attachment.type,
        url: attachment.url,
        name: attachment.file.name,
        size: attachment.file.size,
      } : undefined,
    };

    // Add message to the conversation
    setMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), newMessage],
    }));

    // Update conversation's last message
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, lastMessage: newMessage }
          : conv
      )
    );
  }, []);

  const markAsRead = useCallback((conversationId: string) => {
    // Mark all messages in conversation as read
    setMessages(prev => ({
      ...prev,
      [conversationId]: (prev[conversationId] || []).map(msg => ({ ...msg, isRead: true })),
    }));

    // Reset unread count
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, unreadCount: 0 }
          : conv
      )
    );
  }, []);

  // Sort conversations by last message timestamp
  const sortedConversations = conversations.sort((a, b) => {
    const aTime = a.lastMessage?.timestamp.getTime() || 0;
    const bTime = b.lastMessage?.timestamp.getTime() || 0;
    return bTime - aTime;
  });

  return {
    conversations: sortedConversations,
    messages,
    sendMessage,
    markAsRead,
  };
};
