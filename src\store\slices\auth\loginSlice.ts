import { ProfileData } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { getSession, signIn, signOut } from "next-auth/react";
import { toast } from "react-toastify";

interface LoginData {
  email: string;
  password: string;
}

export type UserResponse = {
  email: string;
  userLastName: string;
  userFirstName: string;
  token: any;
  user?: any;
  id: string;
  userRole?: number;
  roleId?: number;
  profileData?: ProfileData;
  galleryData?: any;
};

interface LoginState {
  loading: boolean;
  error: string | null | any;
  success: boolean;
  token: string | null;
  accessToken: string | null;
  user: null | UserResponse;
  logInData: LoginData;
}

const initialState: LoginState = {
  loading: false,
  error: null,
  success: false,
  token: null,
  accessToken: null,
  user: null,
  logInData: { email: "", password: "" },
};

export const loginUser = createAsyncThunk(
  "auth/loginUser",
  async (
    { email, password }: { email: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await signIn("credentials", {
        redirect: false,
        email,
        password,
      });

      if (response?.ok) {
        const session = await getSession();
        if (session && session?.user) {
          localStorage.setItem("token", JSON.stringify(session?.user?.token));
          sessionStorage.setItem(
            "roleId",
            JSON.stringify(session?.user?.roleId)
          );
            sessionStorage.setItem(
            "userId",
            JSON.stringify(session?.user?.id)
          );
          sessionStorage.setItem(
            "profileData",
            JSON.stringify(session?.user?.profileData)
          );
        }

        return session;
      } else {
        const errorMsg =
          response?.status === 401
            ? "Invalid email or password"
            : "Login failed";
        return rejectWithValue(errorMsg);
      }
    } catch (error: any) {
      console.error("Thunk error:", error);
      return rejectWithValue(error.message || "An unknown error occurred");
    }
  }
);

export const logoutUser = createAsyncThunk(
  "auth/logoutUser",
  async (_, { rejectWithValue }) => {
    try {
      await signOut({ redirect: false });
    } catch (error: any) {
      console.error("Thunk error:", error);
      return rejectWithValue(error.message || "An unknown error occurred");
    }
  }
);

const loginSlice = createSlice({
  name: "login",
  initialState,
  reducers: {
    handleLoginError: (state, action) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.user = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action?.payload && action?.payload?.user;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.loading = false;
        state.user = null;
      })
      .addCase(logoutUser.rejected, (state) => {
        state.loading = false;
        toast.error("Failed to logout");
      });
  },
});

export const { handleLoginError } = loginSlice.actions;
export default loginSlice.reducer;
