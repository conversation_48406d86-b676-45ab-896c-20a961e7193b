# Redux Messages API Integration

This document explains how to use the Redux async thunk to call the connected users API using the existing Redux pattern.

## Overview

The messages slice follows the same pattern as other slices in the application (like `commonSlice.ts`) and provides async thunks to fetch connected users from the API endpoint.

## API Endpoint

```
GET https://api.engageathlete.com/api/notification/v1/messages/connecteduser/{userId}
```

## Files Created

### 1. Redux Slice (`src/store/slices/messagesSlice.ts`)
- **State Management**: Loading, error, connected users, selected user
- **Async Thunks**: API calls with proper error handling
- **Actions**: User selection, clearing data, error management

### 2. Custom Hook (`src/hooks/useMessagesRedux.ts`)
- **Simplified Interface**: Easy-to-use hook for components
- **Type Safety**: Full TypeScript support
- **Action Dispatchers**: Pre-configured dispatch functions

### 3. Demo Component (`src/components/messages/ConnectedUsersRedux.tsx`)
- **Interactive Demo**: Test the API calls
- **Error Handling**: Visual error states
- **Loading States**: Proper loading indicators

## Usage Examples

### Basic Usage in Component

```typescript
import { useMessagesRedux } from '@/hooks/useMessagesRedux';

const MyComponent = () => {
  const {
    loading,
    error,
    connectedUsers,
    fetchConnectedUsersFromLocalStorage,
    fetchConnectedUsersByUserId,
  } = useMessagesRedux();

  useEffect(() => {
    // Fetch from localStorage userId
    fetchConnectedUsersFromLocalStorage();
  }, []);

  const handleFetchSpecificUser = () => {
    // Fetch by specific userId
    fetchConnectedUsersByUserId('170');
  };

  return (
    <div>
      {loading && <p>Loading...</p>}
      {error && <p>Error: {error}</p>}
      {connectedUsers.map(user => (
        <div key={user.id}>
          {user.firstName} {user.lastName} - {user.email}
        </div>
      ))}
    </div>
  );
};
```

### Direct Redux Usage

```typescript
import { useDispatch, useSelector } from 'react-redux';
import { fetchConnectedUsers } from '@/store/slices/messagesSlice';
import { RootState, AppDispatch } from '@/store';

const MyComponent = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error, connectedUsers } = useSelector(
    (state: RootState) => state.messages
  );

  const handleFetch = () => {
    dispatch(fetchConnectedUsers('170'));
  };

  return (
    <div>
      <button onClick={handleFetch}>Fetch Users</button>
      {/* Display users */}
    </div>
  );
};
```

## Async Thunks Available

### 1. `fetchConnectedUsers(userId)`
- **Purpose**: Fetch connected users by specific userId
- **Parameter**: `userId: string | number`
- **Usage**: `dispatch(fetchConnectedUsers('170'))`

### 2. `fetchConnectedUsersFromStorage()`
- **Purpose**: Fetch connected users using userId from localStorage
- **Parameter**: None (reads from localStorage)
- **Usage**: `dispatch(fetchConnectedUsersFromStorage())`

## State Structure

```typescript
interface MessagesState {
  loading: boolean;           // API call in progress
  error: string;             // Error message if any
  connectedUsers: ConnectedUser[]; // Array of connected users
  selectedUserId: number | null;   // Currently selected user ID
}

interface ConnectedUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  galleries: any[];
}
```

## Actions Available

### Async Actions (Thunks)
- `fetchConnectedUsers(userId)` - Fetch by specific userId
- `fetchConnectedUsersFromStorage()` - Fetch from localStorage

### Sync Actions
- `setSelectedUser(userId)` - Set selected user
- `clearConnectedUsers()` - Clear all users and reset state
- `clearError()` - Clear error message

## Error Handling

The async thunks handle various error scenarios:

```typescript
// Network errors
catch (error: any) {
  const errorMessage = error.response?.data?.message || 
                      error.message || 
                      "Failed to fetch connected users";
  return rejectWithValue(errorMessage);
}
```

## Integration Example

The announcement dashboard (`src/components/announcement-dashboard/index.tsx`) shows how to integrate:

```typescript
// Import the hook
import { useMessagesRedux } from '@/hooks/useMessagesRedux';

// Use in component
const {
  loading: usersLoading,
  error: usersError,
  connectedUsers,
  fetchConnectedUsersFromLocalStorage,
} = useMessagesRedux();

// Fetch on component mount
useEffect(() => {
  fetchConnectedUsersFromLocalStorage();
}, [fetchConnectedUsersFromLocalStorage]);
```

## Testing the Implementation

### 1. Using Demo Component
Navigate to the ConnectedUsersRedux component to test:
- Manual userId input
- localStorage fetching
- Error handling
- Loading states

### 2. Using Announcement Dashboard
The announcement dashboard now includes a demo section showing:
- Connected users count
- Sample user names
- Refresh functionality
- Error states

### 3. Browser Console
Check the browser console for detailed logs:
- API request/response
- Redux state changes
- Error messages

## API Response Format

Expected response from the API:

```json
[
  {
    "id": 145,
    "firstName": "First",
    "lastName": "Athlete",
    "email": "<EMAIL>",
    "galleries": []
  }
]
```

## Store Configuration

The messages slice is added to the Redux store:

```typescript
// src/store/index.ts
export const store = configureStore({
  reducer: {
    // ... other slices
    messages: messagesSlice
  },
});
```

## Best Practices

1. **Use the Custom Hook**: Prefer `useMessagesRedux()` over direct Redux calls
2. **Handle Loading States**: Always show loading indicators
3. **Error Handling**: Display user-friendly error messages
4. **Type Safety**: Use TypeScript interfaces for type safety
5. **Console Logging**: Check console for debugging information

## Next Steps

1. **Replace Mock Data**: Update components to use Redux data instead of mock data
2. **Add More Actions**: Extend slice with message sending, conversation management
3. **Optimize Performance**: Add memoization and selective updates
4. **Add Persistence**: Consider persisting state to localStorage
5. **Real-time Updates**: Integrate with socket connections for live updates
